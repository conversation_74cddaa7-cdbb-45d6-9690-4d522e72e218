CREATE DEFINER=`root`@`%` PROCEDURE `photovoltaic`.`proc_calculate_cumulative_kwh`(
    IN powerId VARCHAR(32),
    IN minDate DATE,
    IN maxDate DATE,
    IN minKwhRate DECIMAL(5,2),
    IN maxKwhRate DECIMAL(5,2)
)
BEGIN
    -- 定义变量
    DECLARE collectGap SMALLINT UNSIGNED;
    DECLARE kwhRatio SMALLINT UNSIGNED;
    DECLARE groupTableName VARCHAR(60);
    DECLARE minDateStr VARCHAR(8);
    DECLARE maxDateStr VARCHAR(8);

    -- 设置表名
    SET groupTableName = CONCAT('t_group_collect_', powerId);
    SET minDateStr = DATE_FORMAT(minDate, '%Y%m%d');
    SET maxDateStr = DATE_FORMAT(maxDate, '%Y%m%d');

    -- 获取电站配置信息
    SELECT collect_gap INTO collectGap
    FROM t_app_powerstation
    WHERE id = powerId AND status = 2;

    -- 如果找不到电站配置，退出
    IF collectGap IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Power station configuration not found';
    END IF;

    SET kwhRatio = 3600 / collectGap;

    -- 创建临时表存储所有批次号
    DROP TEMPORARY TABLE IF EXISTS temp_batch_list;
    SET @sql = CONCAT('CREATE TEMPORARY TABLE temp_batch_list AS ',
                     'SELECT DISTINCT batchNo FROM ', groupTableName, ' ',
                     'WHERE LEFT(batchNo,8) IN ("', minDateStr, '","', maxDateStr, '") ',
                     'ORDER BY batchNo');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    -- 创建临时表存储最小日期的累计电量
    DROP TEMPORARY TABLE IF EXISTS temp_min_cumulative;

    -- 统一的处理逻辑
    SET @sql = CONCAT('CREATE TEMPORARY TABLE temp_min_cumulative AS ',
                     'WITH batch_power AS (',
                     '  SELECT batchNo, ',
                     '    CASE WHEN ', IFNULL(minKwhRate, 0), ' >= 100 THEN ',
                     '      SUM(TRUNCATE(avgOutputCurrent,1) * TRUNCATE(sumOutputVoltage,0)) ',
                     '    ELSE ',
                     '      SUM(CASE WHEN avgOutputCurrent >= 1 ',
                     '          THEN TRUNCATE(avgOutputCurrent,1) * TRUNCATE(sumOutputVoltage,0) ',
                     '          ELSE 0 END) ',
                     '    END as batchPower ',
                     '  FROM ', groupTableName, ' ',
                     '  WHERE LEFT(batchNo,8) = "', minDateStr, '" ',
                     '  GROUP BY batchNo ',
                     '), ',
                     'batch_kwh AS (',
                     '  SELECT batchNo, ',
                     '    CASE WHEN batchPower/1000/', kwhRatio, ' > 10 AND ', IFNULL(minKwhRate, 'NULL'), ' IS NOT NULL ',
                     '         THEN (batchPower/1000/', kwhRatio, ') * ', IFNULL(minKwhRate, 100), '/100 ',
                     '         ELSE batchPower/1000/', kwhRatio, ' END as kwh ',
                     '  FROM batch_power ',
                     '), ',
                     'cumulative_kwh AS (',
                     '  SELECT batchNo, ',
                     '    SUM(kwh) OVER (ORDER BY batchNo) as cumulativeKwh ',
                     '  FROM batch_kwh ',
                     ') ',
                     'SELECT batchNo, cumulativeKwh FROM cumulative_kwh');

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    -- 创建临时表存储最大日期的累计电量
    DROP TEMPORARY TABLE IF EXISTS temp_max_cumulative;

    -- 统一的处理逻辑
    SET @sql = CONCAT('CREATE TEMPORARY TABLE temp_max_cumulative AS ',
                     'WITH batch_power AS (',
                     '  SELECT batchNo, ',
                     '    CASE WHEN ', IFNULL(maxKwhRate, 0), ' >= 100 THEN ',
                     '      SUM(TRUNCATE(avgOutputCurrent,1) * TRUNCATE(sumOutputVoltage,0)) ',
                     '    ELSE ',
                     '      SUM(CASE WHEN avgOutputCurrent >= 1 ',
                     '          THEN TRUNCATE(avgOutputCurrent,1) * TRUNCATE(sumOutputVoltage,0) ',
                     '          ELSE 0 END) ',
                     '    END as batchPower ',
                     '  FROM ', groupTableName, ' ',
                     '  WHERE LEFT(batchNo,8) = "', maxDateStr, '" ',
                     '  GROUP BY batchNo ',
                     '), ',
                     'batch_kwh AS (',
                     '  SELECT batchNo, ',
                     '    CASE WHEN batchPower/1000/', kwhRatio, ' > 10 AND ', IFNULL(maxKwhRate, 'NULL'), ' IS NOT NULL ',
                     '         THEN (batchPower/1000/', kwhRatio, ') * ', IFNULL(maxKwhRate, 100), '/100 ',
                     '         ELSE batchPower/1000/', kwhRatio, ' END as kwh ',
                     '  FROM batch_power ',
                     '), ',
                     'cumulative_kwh AS (',
                     '  SELECT batchNo, ',
                     '    SUM(kwh) OVER (ORDER BY batchNo) as cumulativeKwh ',
                     '  FROM batch_kwh ',
                     ') ',
                     'SELECT batchNo, cumulativeKwh FROM cumulative_kwh');

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    -- 插入结果到目标表
    INSERT INTO t_power_kwh_compute (powerId, batchNo, minKwh, maxKwh)
    SELECT
        powerId,
        bl.batchNo,
        IFNULL(tmin.cumulativeKwh, 0) as minKwh,
        IFNULL(tmax.cumulativeKwh, 0) as maxKwh
    FROM temp_batch_list bl
    LEFT JOIN temp_min_cumulative tmin ON bl.batchNo = tmin.batchNo
    LEFT JOIN temp_max_cumulative tmax ON bl.batchNo = tmax.batchNo
    ORDER BY bl.batchNo;

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_batch_list;
    DROP TEMPORARY TABLE IF EXISTS temp_min_cumulative;
    DROP TEMPORARY TABLE IF EXISTS temp_max_cumulative;

END
