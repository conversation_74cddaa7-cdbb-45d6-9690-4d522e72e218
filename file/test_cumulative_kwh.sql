-- 测试存储过程的SQL脚本
-- 基于示例数据验证累计电量计算逻辑

-- 假设电站配置
-- powerId: 'TEST_POWER_STATION'
-- collect_gap: 300 (5分钟采集间隔)
-- kwhRate: 95.5

-- 测试调用存储过程
CALL proc_calculate_cumulative_kwh(
    'TEST_POWER_STATION',  -- 电站ID
    '2025-07-11',          -- 最小电量日期
    '2025-07-12',          -- 最大电量日期
    95.5                   -- kwhRate
);

-- 查看结果
SELECT * FROM t_power_kwh_compute
WHERE powerId = 'TEST_POWER_STATION'
ORDER BY batchNo;

-- 手动验证累计电量计算逻辑的示例查询
-- 以下查询展示了单个日期的累计电量计算过程

WITH batch_power AS (
  SELECT
    batchNo,
    -- 假设kwhRate = 95.5 < 100，使用第二种计算方式
    SUM(CASE WHEN avgOutputCurrent >= 1
        THEN TRUNCATE(avgOutputCurrent,1) * TRUNCATE(sumOutputVoltage,0)
        ELSE 0 END) as batchPower
  FROM t_group_collect_TEST_POWER_STATION
  WHERE LEFT(batchNo,8) = '20250711'
  GROUP BY batchNo
),
batch_kwh AS (
  SELECT
    batchNo,
    -- kwhRatio = 3600/300 = 12
    -- 假设功率不超过10000W，不应用kwhRate修正
    batchPower/1000/12 as kwh
  FROM batch_power
),
cumulative_kwh AS (
  SELECT
    batchNo,
    kwh,
    SUM(kwh) OVER (ORDER BY batchNo) as cumulativeKwh
  FROM batch_kwh
)
SELECT
  batchNo,
  kwh as batchKwh,
  cumulativeKwh
FROM cumulative_kwh
ORDER BY batchNo
LIMIT 10;

-- 验证kwhRate >= 100的处理逻辑
CALL proc_calculate_cumulative_kwh(
    'ANOTHER_TEST_STATION',              -- 另一个测试电站ID
    '2025-07-11',                        -- 最小电量日期
    '2025-07-12',                        -- 最大电量日期
    105.0                                -- kwhRate >= 100
);
