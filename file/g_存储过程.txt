CREATE DEFINER=`root`@`%` PROCEDURE `photovoltaic`.`proc_insert_new_power_converge_data_with_params`(
    IN powId VARCHAR(32), 
    IN specificDate DATE
)
BEGIN
    -- 定义变量
    DECLARE startHour DATETIME DEFAULT CONCAT(specificDate, ' 06:00:00');
    DECLARE endHour DATETIME DEFAULT CONCAT(specificDate, ' 21:00:00');
    DECLARE currentHour DATETIME;
    DECLARE nextHour DATETIME;
    DECLARE nowDay VARCHAR(8) DEFAULT DATE_FORMAT(specificDate, '%Y%m%d');
    DECLARE collectGap SMALLINT UNSIGNED;
    DECLARE kwhRate decimal(5,2);

    DECLARE kwhRatio SMALLINT UNSIGNED;

    -- 定义组串表名称和采集表名称
    DECLARE groupTableName VARCHAR(60);
    DECLARE collectTableName VARCHAR(60);

    -- 设置表名
    SET groupTableName = CONCAT('t_group_collect_', powId);
    SET collectTableName = CONCAT('t_component_collect_', powId);

    -- 初始化当前时间为开始时间
    SET currentHour = startHour;

    -- 循环插入每小时的数据
 

    -- 插入数据到电站汇总表
    SELECT collect_gap,kwh_rate INTO collectGap,kwhRate FROM t_app_powerstation WHERE id = powId AND status = 2;

    SET kwhRatio = 3600 /collectGap;

    -- 根据kwhRate区分计算逻辑
     if(kwhRate is not null and kwhRate >= 100) then
       -- kwhRate >= 100时：直接用avgOutputCurrent*sumOutputVoltage计算功率
       set @stmt=concat('insert into t_powerstation_collect(batchNo,powerStationId,power,kwh,createTime) select "',nowDay,'","',powId,
       '",sum(batchNoPower),CASE WHEN sum(batchNoPower)/1000/',kwhRatio,' > 10 AND ',kwhRate,' is not null THEN (sum(batchNoPower)/1000/',kwhRatio,') * ',kwhRate,'/100 ELSE sum(batchNoPower)/1000/',kwhRatio,' END,now() from (select sum(truncate(avgOutputCurrent,1)*truncate(sumOutputVoltage,0)) as batchNoPower,batchNo
        from ',groupTableName,' where left(batchNo,8)="',nowDay,'" group by batchNo) powerTable');
     else
       -- kwhRate < 100或为null时：avgOutputCurrent >= 1时正常计算，< 1时设为0
       set @stmt=concat('insert into t_powerstation_collect(batchNo,powerStationId,power,kwh,createTime) select "',nowDay,'","',powId,
       '",sum(batchNoPower),CASE WHEN sum(batchNoPower)/1000/',kwhRatio,' > 10 AND ',kwhRate,' is not null THEN (sum(batchNoPower)/1000/',kwhRatio,') * ',kwhRate,'/100 ELSE sum(batchNoPower)/1000/',kwhRatio,' END,now() from (select sum(CASE WHEN avgOutputCurrent >= 1 THEN truncate(avgOutputCurrent,1)*truncate(sumOutputVoltage,0) ELSE 0 END) as batchNoPower,batchNo
        from ',groupTableName,' where left(batchNo,8)="',nowDay,'" group by batchNo) powerTable');
     end if;
   

    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END