CREATE TABLE `t_group_collect_xxx` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `groupId` varchar(64) DEFAULT NULL COMMENT '组串id',
  `batchNo` varchar(32) DEFAULT NULL COMMENT '批次号',
  `avgOutputCurrent` decimal(10,4) DEFAULT NULL COMMENT '组串批次平均电流',
  `sumOutputVoltage` decimal(10,4) DEFAULT NULL COMMENT '组串批次总电压',
  PRIMARY KEY (`id`),
  KEY `idx_batchNo` (`batchNo`(8)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMPRESSION='zlib' COMMENT='组串采集表';