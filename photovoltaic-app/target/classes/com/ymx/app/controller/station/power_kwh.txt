CREATE DEFINER=`root`@`%` PROCEDURE `photovoltaic`.`proc_insert_converge_data`()
BEGIN
	-- 定义现在时间，现在日期，开始时间，结束时间，之前时间等变量
	  DECLARE nowHour datetime default(select DATE_FORMAT(now(),'%Y-%m-%d %H:00:00'));
	  DECLARE startHour datetime default(select DATE_FORMAT(now(),'%Y-%m-%d 05:00:00'));
      DECLARE endHour datetime default(select DATE_FORMAT(now(),'%Y-%m-%d 21:00:00'));
	  DECLARE beforeHour datetime default(select DATE_FORMAT(date_sub(now(), interval 1 hour),'%Y-%m-%d %H:00:00'));
      DECLARE nowDay varchar(8) default(select DATE_FORMAT(curdate(),'%Y%m%d'));

     -- 定义电站id，组串表名称，采集表名称等变量
	  DECLARE powId varchar(32);
	  DECLARE collectGap smallint unsigned;
	  DECLARE kwhRatio smallint unsigned;
	  DECLARE kwhRate decimal(10,2);
	  DECLARE flag int DEFAULT 0;
	  DECLARE groupTableName varchar(60);
	  DECLARE collectTableName varchar(60);


      DECLARE powerInfo_list CURSOR for select id,collect_gap,kwh_rate from t_app_powerstation where status=2;
      DECLARE CONTINUE HANDLER FOR NOT FOUND SET flag=1;
     
       -- 每小时从采集表中取数据插入对应组串汇总表
      if(nowHour>=startHour && nowHour<=endHour) then 

      open powerInfo_list;
      fetch powerInfo_list into powId,collectGap,kwhRate;
     
      while flag<>1 do
      set groupTableName = concat('t_group_collect_',powId);
      set collectTableName = concat('t_component_collect_',powId);
      set @stmt = concat('insert into ',groupTableName,'(batchNo,groupId,avgOutputCurrent,sumOutputVoltage) 
      select DATE_FORMAT(e.collect_time,"%Y%m%d%H%i%s"),c.belongsGroupId,avg(e.output_current/1000.0),sum(e.output_voltage/1000.0) from ',collectTableName,
     ' e,t_app_component c where e.chip_id=c.chipId and e.collect_time>="',beforeHour,'" and e.collect_time<"',nowHour,'" group by e.collect_time,c.belongsGroupId');
     
      
      prepare stmt from @stmt;
      execute stmt;
      deallocate prepare stmt;
     
     -- 到结束时间从组串汇总表取数据插入电站汇总表
     if(nowHour=endHour) then
     SET kwhRatio = 3600/collectGap;

     -- 根据kwhRate区分计算逻辑
     if(kwhRate is not null and kwhRate >= 100) then
       -- kwhRate >= 100时：直接用avgOutputCurrent*sumOutputVoltage计算功率
       set @stmt=concat('insert into t_powerstation_collect(batchNo,powerStationId,power,kwh,createTime) select "',nowDay,'","',powId,
       '",sum(batchNoPower),CASE WHEN sum(batchNoPower)/1000/',kwhRatio,' > 10 AND ',kwhRate,' is not null THEN (sum(batchNoPower)/1000/',kwhRatio,') * ',kwhRate,'/100 ELSE sum(batchNoPower)/1000/',kwhRatio,' END,now() from (select sum(truncate(avgOutputCurrent,1)*truncate(sumOutputVoltage,0)) as batchNoPower,batchNo
        from ',groupTableName,' where left(batchNo,8)="',nowDay,'" group by batchNo) powerTable');
     else
       -- kwhRate < 100或为null时：avgOutputCurrent >= 1时正常计算，< 1时设为0
       set @stmt=concat('insert into t_powerstation_collect(batchNo,powerStationId,power,kwh,createTime) select "',nowDay,'","',powId,
       '",sum(batchNoPower),CASE WHEN sum(batchNoPower)/1000/',kwhRatio,' > 10 AND ',kwhRate,' is not null THEN (sum(batchNoPower)/1000/',kwhRatio,') * ',kwhRate,'/100 ELSE sum(batchNoPower)/1000/',kwhRatio,' END,now() from (select sum(CASE WHEN avgOutputCurrent >= 1 THEN truncate(avgOutputCurrent,1)*truncate(sumOutputVoltage,0) ELSE 0 END) as batchNoPower,batchNo
        from ',groupTableName,' where left(batchNo,8)="',nowDay,'" group by batchNo) powerTable');
     end if;
     
      prepare stmt from @stmt;
      execute stmt;
      deallocate prepare stmt;
     
      end if;
     
      fetch powerInfo_list into powId,collectGap,kwhRate;
      end while;
      close powerInfo_list;
     
      end if;
	
END